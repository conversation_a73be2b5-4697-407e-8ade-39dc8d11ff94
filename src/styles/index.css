/* stylelint-disable at-rule-no-unknown property-no-unknown */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron&display=swap');
/* Import AssuredQuality animations */
@import './AssuredQuality.css';
/* Import Academic Icons animations */
@import './academic-icons.css';
/* Import Animated Icons */
@import './animated-icons.css';
/* Import GSA Logo Animation */
@import './gsa-logo-animation.css';
/* Import Animated Background */
@import './animated-background.css';
/* Import How It Works Icons Animation */
@import './how-it-works-icons.css';
/* Import Testimonials Background Animation */
@import './testimonials-background.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply antialiased text-night bg-white;
  }
}

.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-celeste to-celeste-dark;
}

/* Add subtle shadow to celeste text for better visibility */
.text-celeste {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.2),
              -1px -1px 0 rgba(0, 0, 0, 0.2),
              1px -1px 0 rgba(0, 0, 0, 0.2),
              -1px 1px 0 rgba(0, 0, 0, 0.2),
              0 0 8px rgba(160, 235, 235, 0.4);
}

/* Add subtle shadow to celeste gradient text for better visibility */
.celeste-gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-celeste to-celeste-dark;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.15),
              -1px -1px 0 rgba(0, 0, 0, 0.15),
              1px -1px 0 rgba(0, 0, 0, 0.15),
              -1px 1px 0 rgba(0, 0, 0, 0.15),
              0 0 8px rgba(160, 235, 235, 0.5),
              0 0 12px rgba(125, 222, 222, 0.4);
}

/* Celeste gradient text without shadow - for navbar */
.celeste-gradient-text-no-shadow {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-celeste to-celeste-dark;
}

/* Outline text for high contrast in WhyUs section */
.outline-text {
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.7);
  text-stroke: 1px rgba(0, 0, 0, 0.7);
}

@keyframes border-slide {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(12deg);
  }
  100% {
    transform: translateX(200%) rotate(12deg);
  }
}

@keyframes shine-slow {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.animate-border-slide {
  animation: border-slide 3s linear infinite;
  background-size: 200% 100%;
}

.animate-shine {
  animation: shine 8s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  position: absolute;
  inset: -50%;
  transform: translateX(-100%) rotate(45deg);
}

.animate-shine-slow {
  animation: shine-slow 4s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  position: absolute;
  inset: -50%;
  transform: translateX(-100%) rotate(45deg);
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float1 {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(3px, -3px); }
}

@keyframes float2 {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(-3px, 3px); }
}

@keyframes float3 {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(3px, 3px); }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
  background-size: 200% 200%;
}

.animate-float1 {
  animation: float1 3s ease-in-out infinite;
}

.animate-float2 {
  animation: float2 3s ease-in-out infinite;
  animation-delay: 0.2s;
}

.animate-float3 {
  animation: float3 3s ease-in-out infinite;
  animation-delay: 0.4s;
}
