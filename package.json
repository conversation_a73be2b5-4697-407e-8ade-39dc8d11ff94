{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://www.gradespark.org/", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/cannon": "^6.6.0", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@supabase/supabase-js": "^2.39.7", "@types/react-slick": "^0.23.13", "@types/three": "^0.176.0", "framer-motion": "^12.12.2", "gray-matter": "^4.0.3", "gsap": "^3.12.7", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "swiper": "^11.1.14", "three": "^0.177.0", "three-mesh-bvh": "^0.9.0", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.14.1", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.1", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.18", "eslint": "^9.27.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vitest": "^0.5.4", "gh-pages": "^6.2.0", "globals": "^16.2.0", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-svgo": "^11.0.1", "jsdom": "^26.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.16", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vitest": "^3.1.4"}}